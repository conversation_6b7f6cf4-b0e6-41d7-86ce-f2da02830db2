#!/usr/bin/env bash
set -euo pipefail
__prevEnv__="$(env)"

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
export NX_WORKSPACE_ROOT=${DIR}

PATH_add node_modules/.bin

# 注意：FIXTURE_DIR 已删除，测试文件路径改为相对路径

# -----------------------------------------------------------------------------
# Application Settings
# -----------------------------------------------------------------------------
export WORKSPACE_NAME="saito"
export API_PORT=8716

# 注意：以下环境变量已删除，改为动态获取或使用默认值：
# - LOG_LEVEL (使用 NestJS 默认日志级别)
# - NODE_ENV (由运行环境自动设置)
# - API_SERVER_BASE_PATH (从注册信息获取)
# - SIGHTAI_BACKEND_URL (动态构建)
# - ENABLE_DEBUG_INFO (已删除，使用标准日志控制)

# -----------------------------------------------------------------------------
# Model Inference Framework Configuration
# -----------------------------------------------------------------------------
export MODEL_INFERENCE_FRAMEWORK="ollama"

# Ollama Configuration
export OLLAMA_API_URL="http://127.0.0.1:11434"
export OLLAMA_MODEL="deepscaler"

# vLLM Configuration
export VLLM_API_URL="http://localhost:8000"
export VLLM_MODEL="microsoft/DialoGPT-medium"
export VLLM_PORT="8000"
export VLLM_HOST="0.0.0.0"
export VLLM_GPU_MEMORY_UTILIZATION="0.9"
export VLLM_MAX_MODEL_LEN="4096"

# -----------------------------------------------------------------------------
# Node & Blockchain Configuration
# -----------------------------------------------------------------------------
# 注意：NODE_DATABASE_URL 已删除，暂时不需要数据库连接

# 注意：以下环境变量已改为动态获取，不再需要手动配置：
# - GATEWAY_API_URL (从注册信息获取)
# - NODE_CODE (从注册信息获取)
# - REWARD_ADDRESS (从注册信息获取)
# - GATEWAY_API_KEY (从注册信息获取)
# - DEVICE_TYPE (动态检测系统类型)
# - GPU_MODEL (动态检测 GPU 型号)

if [[ -f .envrc.override ]]; then
  source_env .envrc.override
fi

# export updated ENV of this file
node "${NX_WORKSPACE_ROOT}/tools/bin/get-env" "${__prevEnv__}" "$(env)" >"${NX_WORKSPACE_ROOT}/.env" &

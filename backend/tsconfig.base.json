{
  "compileOnSave": false,
  "compilerOptions": {
    "declaration": false,
    "sourceMap": true,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "strict": true,
    "target": "ES2021",
    "module": "CommonJS",
    "lib": ["es2021"],
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "composite": false,
    "noEmitOnError": false,
    "baseUrl": ".",
    "typeRoots": ["node_modules/@types", "types"],
    "paths": {
      "@saito/api-server/*": ["packages/apps/api-server/src/*"],
      "@saito/common": ["packages/libs/common/src/index.ts"],
      "@saito/configs": ["packages/libs/configs/src/index.ts"],
      "@saito/models": ["packages/libs/models/src/index.ts"],
      "@saito/persistent": ["packages/libs/persistent/src/index.ts"],
      "@saito/miner": ["packages/libs/miner/src/index.ts"],
      "@saito/tunnel": ["packages/libs/tunnel/src/index.ts"],
      "@saito/device-status": ["packages/libs/device-status/src/index.ts"],
      "@saito/task-sync": ["packages/libs/task-sync/src/index.ts"],
      "@saito/model-reporting": ["packages/libs/model-reporting/src/index.ts"],
      "@saito/model-inference-client": ["packages/libs/model-inference-client/src/index.ts"],
      "@saito/model-inference-framework-management": ["packages/libs/model-inference-framework-management/src/index.ts"],
      "@saito/earnings-tracking": ["packages/libs/earnings-tracking/src/index.ts"],
      "@saito/ollama": ["packages/libs/ollama1/src/index.ts"],
      "@saito/vllm": ["packages/libs/vllm1/src/index.ts"],
    }
  },
  "exclude": ["node_modules", "tmp"],
  "ts-node": {
    "require": ["tsconfig-paths/register"]
  }
}

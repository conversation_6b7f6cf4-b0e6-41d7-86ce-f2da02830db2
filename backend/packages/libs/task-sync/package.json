{"name": "@saito/task-sync", "version": "0.0.1", "dependencies": {"@saito/common": "*", "@saito/models": "*", "@saito/persistent": "*", "@saito/ethstorage": "*", "@saito/miner": "*", "@nestjs/common": "^10.0.0", "ethers": "^6.13.5", "memoizee": "^0.4.15", "openpipe": "*", "slonik": "^37.2.0", "zod": "^3.22.4", "@nestjs/schedule": "^5.0.1", "got-cjs": "^12.5.4"}, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts"}
{"name": "model-inference-client", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/libs/model-inference-client/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/libs/model-inference-client", "main": "packages/libs/model-inference-client/src/index.ts", "tsConfig": "packages/libs/model-inference-client/tsconfig.lib.json", "assets": ["packages/libs/model-inference-client/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/libs/model-inference-client/**/*.ts", "packages/libs/model-inference-client/package.json"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/libs/model-inference-client/jest.config.ts"}}}, "tags": []}
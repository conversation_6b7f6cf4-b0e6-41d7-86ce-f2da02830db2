{"name": "@saito/earnings-tracking", "version": "0.0.1", "description": "Earnings tracking module for API calls and task management", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts"}}, "scripts": {"build": "tsc", "test": "jest"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "rxjs": "^7.8.0", "express": "^4.18.0", "crypto": "^1.0.1"}, "peerDependencies": {"@saito/miner": "*", "@saito/device-status": "*", "@saito/model-inference-client": "*"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "jest": "^29.0.0"}}
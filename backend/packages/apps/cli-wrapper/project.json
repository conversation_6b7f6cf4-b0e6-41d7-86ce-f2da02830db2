{"name": "cli-wrapper", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/apps/cli-wrapper/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/packages/apps/cli-wrapper", "main": "packages/apps/cli-wrapper/src/main.ts", "tsConfig": "packages/apps/cli-wrapper/tsconfig.app.json", "webpackConfig": "packages/apps/cli-wrapper/webpack.config.js", "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "packages/apps/cli-wrapper/src/environments/environment.ts", "with": "packages/apps/cli-wrapper/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "cli-wrapper:build"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/apps/cli-wrapper/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/apps/cli-wrapper/jest.config.ts"}}}, "tags": []}
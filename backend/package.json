{"name": "@saito-miner/sightai", "version": "0.0.0", "license": "UNLICENSED", "scripts": {"postinstall": "patch-package", "build:optimized": "node scripts/optimized-build.js", "pkg:yao": "npx @yao-pkg/pkg --compress GZip .", "build:production": "pnpm nx build cli-wrapper --configuration=production && pnpm run pkg:yao", "analyze:bundle": "pnpm nx build cli-wrapper --configuration=production --stats-json && npx webpack-bundle-analyzer dist/packages/apps/cli-wrapper/stats.json", "analyze:deps": "node scripts/analyze-dependencies.js", "clean:all": "rm -rf dist dist-pkg node_modules/.cache"}, "private": true, "dependencies": {"@inquirer/prompts": "^3.3.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.2.10", "@nestjs/core": "^10.2.10", "@nestjs/microservices": "^10.2.10", "@nestjs/platform-express": "^10.2.10", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "7.1.16", "@oclif/core": "3.12.0", "@oclif/plugin-help": "6.0.7", "@oclif/plugin-plugins": "4.1.8", "ansi-regex": "^5.0.1", "axios": "^1.8.1", "boxen": "^5.1.2", "bs58": "^6.0.0", "chalk": "^4.1.2", "chokidar": "^4.0.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cli-table3": "^0.6.5", "commander": "^11.1.0", "dedent": "^1.5.1", "dnum-cjs": "^2.9.0", "dotenv": "^16.3.1", "express": "^4.18.2", "fast-json-stable-stringify": "^2.1.0", "got-cjs": "^12.5.4", "gpu-info": "^0.0.1", "inquirer": "^8.2.6", "ip": "^2.0.1", "jsonwebtoken": "^9.0.2", "level": "^10.0.0", "lodash": "^4.17.21", "memoizee": "^0.4.15", "nestjs-pino": "^3.5.0", "nestjs-zod": "^3.0.0", "node-nvidia-smi": "^1.0.0", "oclif": "^4.0.4", "openai": "^4.17.5", "ora": "^5.4.1", "p-queue": "^6.6.2", "p-retry": "^4.6.2", "pino": "^8.16.2", "pino-http": "^8.5.1", "pino-pretty": "^10.2.3", "ramda": "^0.29.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "safe-json-stringify": "^1.2.0", "slonik": "^37.2.0", "slonik-interceptor-query-logging": "^1.4.7", "socket.io-client": "^4.8.1", "strip-ansi": "^6.0.1", "systeminformation": "^5.25.11", "tslib": "^2.6.2", "tweetnacl": "^1.0.3", "uuid": "^9.0.1", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.0"}, "devDependencies": {"@milahu/patch-package": "^6.4.14", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.10", "@nx-tools/container-metadata": "^5.1.0", "@nx-tools/nx-container": "^5.1.0", "@nx/devkit": "17.1.3", "@nx/eslint": "17.1.3", "@nx/eslint-plugin": "17.1.3", "@nx/jest": "17.1.3", "@nx/js": "17.1.3", "@nx/nest": "17.1.3", "@nx/node": "17.1.3", "@nx/webpack": "17.1.3", "@nx/workspace": "17.1.3", "@swc-node/register": "1.6.8", "@swc/core": "^1.3.99", "@swc/helpers": "^0.5.17", "@telegraf/types": "^6.9.1", "@types/express": "^4.17.20", "@types/inquirer": "^8.2.11", "@types/ip": "^1.1.3", "@types/jest": "^29.5.10", "@types/jsonwebtoken": "^9.0.8", "@types/lodash": "^4.14.202", "@types/memoizee": "^0.4.11", "@types/node": "20.10.0", "@types/ramda": "^0.29.9", "@types/safe-json-stringify": "^1.1.5", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "6.12.0", "@typescript-eslint/parser": "6.12.0", "archiver": "^7.0.1", "copy-webpack-plugin": "^13.0.0", "eslint": "~8.54.0", "eslint-config-prettier": "9.0.0", "fs-extra": "^11.1.1", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "nats": "^2.29.3", "npm-check-updates": "^16.14.11", "nx": "^17.1.3", "nx-cloud": "16.5.2", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "terser-webpack-plugin": "^5.3.14", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "10.9.1", "tsconfig-paths": "4.2.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "~5.2.2", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.2"}, "prettier": {"singleQuote": true, "semi": true, "tabWidth": 2, "arrowParens": "avoid", "trailingComma": "all", "printWidth": 80, "plugins": ["prettier-plugin-organize-imports"]}, "bin": {"sight": "dist/packages/apps/cli-wrapper/main.js"}, "pkg": {"scripts": ["dist/packages/apps/cli-wrapper/**/*.js"], "assets": ["dist/**/*", ".env", "node_modules/.pnpm/classic-level@3.0.0/node_modules/classic-level/prebuilds/**/*"], "targets": ["node20-mac-arm64"], "outputPath": "dist-pkg", "options": ["--compress", "GZip"]}}